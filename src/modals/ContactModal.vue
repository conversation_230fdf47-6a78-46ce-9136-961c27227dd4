<template>
  <!-- Modal Overlay -->
  <Transition name="modal">
    <div v-if="visible" class="modal-overlay">
      <!-- Background Blur -->
      <div class="modal-backdrop" @click="close"></div>
      
      <!-- Modal Content -->
      <div class="modal-container">
        <div class="modal-content">
          <!-- Close Button -->
          <button 
            class="modal-close-btn"
            @click="close"
            aria-label="Close modal"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>

          <!-- Header -->
          <div class="modal-header">
            <h2 class="modal-title coco">Let's Chat</h2>
            <p class="modal-subtitle">
              Drop me a line and I'll get back to you within 24 hours. 
              Or if you prefer, I can send over a Calendly link to book a quick call.
            </p>
          </div>

          <!-- Form -->
          <form @submit.prevent="handleSubmit" class="modal-form">
            <div class="form-group">
              <label class="form-label">Name *</label>
              <input 
                v-model="form.name" 
                type="text" 
                required 
                class="form-input"
                placeholder="Your name"
              />
            </div>
            
            <div class="form-group">
              <label class="form-label">Email *</label>
              <input 
                v-model="form.email" 
                type="email" 
                required 
                class="form-input"
                placeholder="<EMAIL>"
              />
            </div>
            
            <div class="form-group">
              <label class="form-label">Message (optional)</label>
              <textarea 
                v-model="form.message" 
                class="form-input form-textarea" 
                rows="4"
                placeholder="Tell me about your project..."
              />
            </div>
            
            <div class="form-actions">
              <BaseButton 
                type="submit" 
                variant="secondary"
                size="lg"
                class="submit-btn"
                :loading="isSubmitting"
              >
                {{ isSubmitting ? 'Sending...' : 'Send Message' }}
              </BaseButton>
              
              <p class="form-note">
                I'll get back to you within 24 hours
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue'
import BaseButton from '@/components/BaseButton.vue'

interface ContactForm {
  name: string
  email: string
  message: string
}

const visible = defineModel<boolean>('visible', { default: false })
const form = ref<ContactForm>({ name: '', email: '', message: '' })
const isSubmitting = ref(false)

const close = () => {
  visible.value = false
}

// Handle ESC key to close modal
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && visible.value) {
    close()
  }
}

// Manage body scroll when modal is open/closed
watch(visible, (isVisible) => {
  if (isVisible) {
    document.body.classList.add('modal-open')
  } else {
    document.body.classList.remove('modal-open')
  }
}, { immediate: true })

// Add/remove keyboard event listeners
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  // Clean up body class if component is unmounted while modal is open
  document.body.classList.remove('modal-open')
})

const handleSubmit = async () => {
  isSubmitting.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    console.log('Sending:', form.value)
    
    // Reset form
    form.value = { name: '', email: '', message: '' }
    
    // Show success message (you could replace this with a toast notification)
    alert('Message sent! I\'ll be in touch shortly.')
    
    close()
  } catch (error) {
    console.error('Error sending message:', error)
    alert('Sorry, there was an error sending your message. Please try again.')
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style scoped>
/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 50;
  display: flex;;
}

/* Background Blur */
.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Modal Container */
.modal-container {
  position: relative;
  flex: 1;
  width: 100dvw;
  height: 100dvh;
  overflow-y: auto;
  z-index: 10;
}

/* Modal Content */
.modal-content {
  background: white;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  position: relative;
  padding: 4rem;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 4rem;
  height: 100%;
}

/* Close Button */
.modal-close-btn {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  transition: all 0.2s ease;
  cursor: pointer;
}

.modal-close-btn:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #1e293b;
  transform: scale(1.05);
}

/* Header */
.modal-header {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.modal-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.2;
}

.modal-subtitle {
  font-size: 1.125rem;
  color: #64748b;
  line-height: 1.6;
  margin: 0 auto;
}

/* Form */
.modal-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  flex: 1;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
}

.form-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.75rem;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: #fafafa;
}

.form-input:focus {
  outline: none;
  border-color: #1e293b;
  background: white;
  box-shadow: 0 0 0 3px rgba(30, 41, 59, 0.1);
}

.form-input::placeholder {
  color: #9ca3af;
}

.form-textarea {
  resize: vertical;
  min-height: 6rem;
}

/* Form Actions */
.form-actions {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.submit-btn {
  width: 100%;
  max-width: 20rem;
}

.form-note {
  font-size: 0.875rem;
  color: #64748b;
  text-align: center;
}

/* Transitions */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-enter-from .modal-content,
.modal-leave-to .modal-content {
  transform: scale(0.95) translateY(1rem);
}

.modal-enter-to .modal-content,
.modal-leave-from .modal-content {
  transform: scale(1) translateY(0);
}

/* Responsive Design */
@media (max-width: 640px) {
  .modal-content {
    padding: 2rem 1.5rem;
    margin: 0.5rem;
    border-radius: 1rem;
  }

  .modal-title {
    font-size: 2rem;
  }

  .modal-subtitle {
    margin: 0;
  }

  .modal-close-btn {
    top: 1rem;
    right: 1rem;
    width: 2rem;
    height: 2rem;
  }
}
</style>
