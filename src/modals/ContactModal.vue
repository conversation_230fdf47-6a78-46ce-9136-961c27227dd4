<template>
  <div v-if="visible" class="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
    <div class="bg-white rounded-xl shadow-lg w-full max-w-md p-6 relative">
      <button class="absolute top-3 right-4 text-gray-400 hover:text-black" @click="close">×</button>
      <h2 class="text-xl font-semibold mb-4">Book a Call</h2>
      <p class="text-sm text-gray-600 mb-6">Leave your details and I’ll send over a Calendly link or get in touch directly.</p>

      <form @submit.prevent="handleSubmit" class="space-y-4">
        <div>
          <label class="block text-sm font-medium">Name *</label>
          <input v-model="form.name" type="text" required class="input" />
        </div>
        <div>
          <label class="block text-sm font-medium">Email *</label>
          <input v-model="form.email" type="email" required class="input" />
        </div>
        <div>
          <label class="block text-sm font-medium">Message (optional)</label>
          <textarea v-model="form.message" class="input" rows="3" />
        </div>
        <button type="submit" class="w-full bg-black text-white py-2 rounded hover:bg-gray-900">
          Send Enquiry
        </button>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const visible = defineModel('visible', { default: false })
const form = ref({ name: '', email: '', message: '' })

const close = () => (visible.value = false)

const handleSubmit = () => {
  // Replace with your submission logic
  console.log('Sending:', form.value)
  alert('Message sent! I’ll be in touch shortly.')
  close()
}
</script>

<style scoped>
.input {
  @apply w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black;
}
</style>