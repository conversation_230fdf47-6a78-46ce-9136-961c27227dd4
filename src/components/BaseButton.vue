<template>
  <component
    :is="tag"
    :href="href"
    :target="target"
    :rel="rel"
    :type="type"
    :disabled="disabled"
    :class="buttonClasses"
    :style="customStyle"
    @click="handleClick"
  >
    <span class="z-10 flex items-center gap-2" :class="{ 'pr-2': hasArrow }">
      <!-- Loading Spinner -->
      <svg
        v-if="loading"
        class="animate-spin h-4 w-4"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        />
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
      <slot />
    </span>
    
    <!-- Animated Arrow -->
    <div 
      v-if="hasArrow"
      class="absolute right-1 inline-flex items-center justify-end rounded-full transition-[width] group-hover:w-[calc(100%-8px)]"
      :class="arrowClasses"
    >
      <div class="mr-3.5 flex items-center justify-center">
        <svg 
          width="15" 
          height="15" 
          viewBox="0 0 15 15" 
          fill="none" 
          xmlns="http://www.w3.org/2000/svg" 
          class="h-5 w-5"
          :class="arrowIconClasses"
        >
          <path 
            d="M8.14645 3.14645C8.34171 2.95118 8.65829 2.95118 8.85355 3.14645L12.8536 7.14645C13.0488 7.34171 13.0488 7.65829 12.8536 7.85355L8.85355 11.8536C8.65829 12.0488 8.34171 12.0488 8.14645 11.8536C7.95118 11.6583 7.95118 11.3417 8.14645 11.1464L11.2929 8H2.5C2.22386 8 2 7.77614 2 7.5C2 7.22386 2.22386 7 2.5 7H11.2929L8.14645 3.85355C7.95118 3.65829 7.95118 3.34171 8.14645 3.14645Z" 
            fill="currentColor" 
            fill-rule="evenodd" 
            clip-rule="evenodd"
          />
        </svg>
      </div>
    </div>
  </component>
</template>

<script setup lang="ts">
import { computed } from 'vue'

export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  href?: string
  target?: string
  rel?: string
  type?: 'button' | 'submit' | 'reset'
  disabled?: boolean
  loading?: boolean
  hasArrow?: boolean
  customColor?: string
  customBorderColor?: string
  customHoverColor?: string
}

const props = withDefaults(defineProps<ButtonProps>(), {
  variant: 'primary',
  size: 'md',
  type: 'button',
  disabled: false,
  loading: false,
  hasArrow: false
})

const emit = defineEmits<{
  click: [event: Event]
}>()

// Determine the component tag
const tag = computed(() => props.href ? 'a' : 'button')

// Handle click events
const handleClick = (event: Event) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}

// Base classes
const baseClasses = computed(() => [
  'group relative inline-flex items-center justify-center font-medium transition-all duration-300 ease-in-out',
  'focus:outline-none focus:ring-2 focus:ring-offset-2',
  {
    'cursor-not-allowed opacity-50': props.disabled || props.loading,
    'cursor-pointer': !props.disabled && !props.loading
  }
])

// Size classes
const sizeClasses = computed(() => {
  const sizes = {
    sm: 'px-4 py-2 text-sm rounded-full h-10',
    md: 'px-6 py-3 text-base rounded-full h-12',
    lg: 'px-6 py-1 text-base rounded-full h-[calc(48px+8px)]'
  }
  return sizes[props.size]
})

// Variant classes
const variantClasses = computed(() => {
  if (props.customColor) {
    // Custom color variant (like the header contact button)
    return [
      'border border-current hover:bg-current hover:text-white',
      'focus:ring-current'
    ]
  }

  const variants = {
    primary: [
      'bg-slate-700 text-white border border-slate-700',
      'hover:bg-slate-600 hover:border-slate-600',
      'focus:ring-slate-500'
    ],
    secondary: [
      'bg-slate-900 text-white border border-slate-900',
      'hover:bg-slate-800 hover:border-slate-800',
      'focus:ring-slate-700'
    ],
    outline: [
      'bg-transparent text-slate-700 border border-slate-300',
      'hover:bg-slate-50 hover:border-slate-400',
      'focus:ring-slate-500'
    ],
    ghost: [
      'bg-transparent text-slate-700 border border-transparent',
      'hover:bg-slate-100',
      'focus:ring-slate-500'
    ]
  }
  return variants[props.variant]
})

// Arrow background classes
const arrowClasses = computed(() => {
  if (props.customColor) {
    return 'bg-current bg-opacity-20'
  }

  const arrowBgs = {
    primary: 'bg-slate-500',
    secondary: 'bg-slate-500', 
    outline: 'bg-slate-200',
    ghost: 'bg-slate-200'
  }
  
  const baseSize = props.size === 'lg' ? 'h-12 w-12' : props.size === 'sm' ? 'h-8 w-8' : 'h-10 w-10'
  
  return [arrowBgs[props.variant], baseSize]
})

// Arrow icon classes
const arrowIconClasses = computed(() => {
  if (props.customColor) {
    return 'text-current'
  }
  
  return 'text-white'
})

// Combined button classes
const buttonClasses = computed(() => [
  ...baseClasses.value,
  sizeClasses.value,
  ...variantClasses.value
])

// Custom style for dynamic colors
const customStyle = computed(() => {
  if (props.customColor) {
    const style: Record<string, string> = {
      color: props.customColor
    }
    
    if (props.customBorderColor) {
      style.borderColor = props.customBorderColor
    } else {
      style.borderColor = props.customColor
    }
    
    return style
  }
  return {}
})
</script>
