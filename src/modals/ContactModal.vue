<template>
  <div v-if="visible" class="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
    <div class="bg-white rounded-xl shadow-lg w-full max-w-md p-6 relative">
      <button class="absolute top-3 right-4 text-slate-400 hover:text-black" @click="close">×</button>
      <h2 class="text-xl font-semibold mb-4">Book a Call</h2>
      <p class="text-sm text-slate-600 mb-6">Leave your details and I’ll send over a Calendly link or get in touch directly.</p>

      <form @submit.prevent="handleSubmit" class="space-y-4">
        <div>
          <label class="block text-sm font-medium">Name *</label>
          <input v-model="form.name" type="text" required class="input" />
        </div>
        <div>
          <label class="block text-sm font-medium">Email *</label>
          <input v-model="form.email" type="email" required class="input" />
        </div>
        <div>
          <label class="block text-sm font-medium">Message (optional)</label>
          <textarea v-model="form.message" class="input" rows="3" />
        </div>
        <BaseButton
          type="submit"
          variant="secondary"
          size="md"
          class="w-full"
        >
          Send Enquiry
        </BaseButton>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import BaseButton from '@/components/BaseButton.vue'

interface ContactForm {
  name: string
  email: string
  message: string
}

const visible = defineModel<boolean>('visible', { default: false })
const form = ref<ContactForm>({ name: '', email: '', message: '' })

const close = () => (visible.value = false)

const handleSubmit = () => {
  // Replace with your submission logic
  console.log('Sending:', form.value)
  alert('Message sent! I’ll be in touch shortly.')
  close()
}
</script>

<style scoped>
.input {
  width: 100%;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  outline: none;
}

.input:focus {
  outline: none;
  box-shadow: 0 0 0 2px #000;
}
</style>