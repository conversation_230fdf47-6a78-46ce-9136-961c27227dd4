<template>
  <!-- Sidebar Modal Overlay -->
  <Transition :name="`sidebar-${side}`">
    <div v-if="visible" class="fixed inset-0 z-50 flex items-stretch" :class="side === 'right' ? 'justify-end' : 'justify-start'">
      <!-- Background Blur -->
      <div
        class="absolute inset-0 bg-black/40 backdrop-blur-lg"
        @click="close"
      ></div>

      <!-- Sidebar Content -->
      <div class="relative w-full sm:max-w-md h-screen z-10 flex flex-col">
        <div class="bg-white h-full shadow-2xl flex flex-col overflow-y-auto">
          <!-- Close Button -->
          <button
            class="absolute top-6 w-10 h-10 rounded-full bg-slate-100 hover:bg-slate-200 flex items-center justify-center text-slate-600 hover:text-slate-900 transition-all duration-200 z-10"
            :class="side === 'right' ? 'right-6' : 'left-6'"
            @click="close"
            aria-label="Close sidebar"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>

          <!-- Header -->
          <div class="px-8 pt-12 pb-8 border-b border-slate-100">
            <h2 class="text-3xl font-semibold text-slate-900 mb-4 coco">Let's Chat</h2>
            <p class="text-slate-600 leading-relaxed">
              Drop me a line and I'll get back to you within 24 hours.
              Or if you prefer, I can send over a Calendly link to book a quick call.
            </p>
          </div>

          <!-- Form -->
          <form @submit.prevent="handleSubmit" class="flex-1 px-8 py-8 space-y-6">
            <div class="space-y-2">
              <label class="block text-sm font-semibold text-slate-700">Name *</label>
              <input
                v-model="form.name"
                type="text"
                required
                class="w-full px-4 py-3 border-2 border-slate-200 rounded-xl focus:border-slate-900 focus:outline-none transition-colors bg-slate-50 focus:bg-white"
                placeholder="Your name"
              />
            </div>

            <div class="space-y-2">
              <label class="block text-sm font-semibold text-slate-700">Email *</label>
              <input
                v-model="form.email"
                type="email"
                required
                class="w-full px-4 py-3 border-2 border-slate-200 rounded-xl focus:border-slate-900 focus:outline-none transition-colors bg-slate-50 focus:bg-white"
                placeholder="<EMAIL>"
              />
            </div>

            <div class="space-y-2">
              <label class="block text-sm font-semibold text-slate-700">Message (optional)</label>
              <textarea
                v-model="form.message"
                class="w-full px-4 py-3 border-2 border-slate-200 rounded-xl focus:border-slate-900 focus:outline-none transition-colors bg-slate-50 focus:bg-white resize-none"
                rows="4"
                placeholder="Tell me about your project..."
              />
            </div>

            <!-- Submit Button -->
            <div class="pt-4">
              <BaseButton
                type="submit"
                variant="secondary"
                size="lg"
                class="w-full"
                :loading="isSubmitting"
              >
                {{ isSubmitting ? 'Sending...' : 'Send Message' }}
              </BaseButton>

              <p class="text-sm text-slate-500 text-center mt-4">
                I'll get back to you within 24 hours
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue'
import BaseButton from '@/components/BaseButton.vue'

interface ContactForm {
  name: string
  email: string
  message: string
}

interface Props {
  side?: 'left' | 'right'
}

const props = withDefaults(defineProps<Props>(), {
  side: 'right'
})

const visible = defineModel<boolean>('visible', { default: false })
const form = ref<ContactForm>({ name: '', email: '', message: '' })
const isSubmitting = ref(false)

const close = () => {
  visible.value = false
}

// Handle ESC key to close modal
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && visible.value) {
    close()
  }
}

// Manage body scroll when modal is open/closed
watch(visible, (isVisible) => {
  if (isVisible) {
    document.body.classList.add('modal-open')
  } else {
    document.body.classList.remove('modal-open')
  }
}, { immediate: true })

// Add/remove keyboard event listeners
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  // Clean up body class if component is unmounted while modal is open
  document.body.classList.remove('modal-open')
})

const handleSubmit = async () => {
  isSubmitting.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    console.log('Sending:', form.value)
    
    // Reset form
    form.value = { name: '', email: '', message: '' }
    
    // Show a success message (you could replace this with a toast notification)
    alert('Message sent! I\'ll be in touch shortly.')
    
    close()
  } catch (error) {
    console.error('Error sending message:', error)
    alert('Sorry, there was an error sending your message. Please try again.')
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style scoped>
/* Sidebar slide-in transitions - Right side */
.sidebar-right-enter-active,
.sidebar-right-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-right-enter-from,
.sidebar-right-leave-to {
  opacity: 0;
}

.sidebar-right-enter-from .bg-white,
.sidebar-right-leave-to .bg-white {
  transform: translateX(100%);
}

.sidebar-right-enter-to .bg-white,
.sidebar-right-leave-from .bg-white {
  transform: translateX(0);
}

/* Sidebar slide-in transitions - Left side */
.sidebar-left-enter-active,
.sidebar-left-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-left-enter-from,
.sidebar-left-leave-to {
  opacity: 0;
}

.sidebar-left-enter-from .bg-white,
.sidebar-left-leave-to .bg-white {
  transform: translateX(-100%);
}

.sidebar-left-enter-to .bg-white,
.sidebar-left-leave-from .bg-white {
  transform: translateX(0);
}
</style>
